"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/app/ui/risk-evaluations/risk-evaluations.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/risk-evaluations/risk-evaluations.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskEvaluations; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _logbook_forms_risk_analysis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/forms/risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _logbook_forms_bar_crossing_risk_analysis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../logbook/forms/bar-crossing-risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx\");\n/* harmony import */ var _components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/collapsible-data-table */ \"(app-pages-browser)/./src/components/collapsible-data-table.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Helper function to get the display name for risk evaluation type\nconst getRiskEvaluationType = (checklist)=>{\n    const typeMap = {\n        TowingChecklist: \"Towing Checklist\",\n        DangerousGoods: \"Dangerous Goods\",\n        BarCrossingChecklist: \"Bar Crossing Checklist\"\n    };\n    let typeName = typeMap[checklist.type] || checklist.type;\n    // Add ID if available\n    if (checklist.type === \"TowingChecklist\" && checklist.towingChecklistID && checklist.towingChecklistID > 0) {\n        typeName += \": #\".concat(checklist.towingChecklistID);\n    } else if (checklist.type === \"DangerousGoods\" && checklist.dangerousGoodsID && checklist.dangerousGoodsID > 0) {\n        typeName += \": #\".concat(checklist.dangerousGoodsID);\n    } else if (checklist.type === \"BarCrossingChecklist\" && checklist.barCrossingChecklistID && checklist.barCrossingChecklistID > 0) {\n        typeName += \": #\".concat(checklist.barCrossingChecklistID);\n    }\n    return typeName;\n};\n// Helper function to get member name\nconst getMemberName = (checklist)=>{\n    let member = null;\n    if (checklist.type === \"TowingChecklist\") {\n        var _checklist_towingChecklist;\n        member = (_checklist_towingChecklist = checklist.towingChecklist) === null || _checklist_towingChecklist === void 0 ? void 0 : _checklist_towingChecklist.member;\n    } else if (checklist.type === \"DangerousGoods\") {\n        var _checklist_dangerousGoods;\n        member = (_checklist_dangerousGoods = checklist.dangerousGoods) === null || _checklist_dangerousGoods === void 0 ? void 0 : _checklist_dangerousGoods.member;\n    } else if (checklist.type === \"BarCrossingChecklist\") {\n        var _checklist_barCrossingChecklist;\n        member = (_checklist_barCrossingChecklist = checklist.barCrossingChecklist) === null || _checklist_barCrossingChecklist === void 0 ? void 0 : _checklist_barCrossingChecklist.member;\n    }\n    if (!member) return \"\";\n    const firstName = member.firstName || \"\";\n    const surname = member.surname || \"\";\n    return \"\".concat(firstName, \" \").concat(surname).trim();\n};\n// Helper function to format date\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(new Date(dateString), \"dd/MM/yyyy\");\n    } catch (e) {\n        return dateString;\n    }\n};\nfunction RiskEvaluations() {\n    _s();\n    const [riskFactors, setRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Define columns for the collapsible table\n    const columns = (0,_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__.createColumns)([\n        {\n            accessorKey: \"type\",\n            header: \"Risk Evaluations\",\n            cell: (param)=>{\n                let { row } = param;\n                var _row_original_vessel;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-nowrap\",\n                            children: getRiskEvaluationType(row.original)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm landscape:hidden text-card-foreground/80\",\n                            children: [\n                                \"Memeber: \",\n                                getMemberName(row.original)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm landscape:hidden flex justify-between text-card-foreground/80\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        \"Date:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatDate(row.original.created)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" tablet-md:hidden\",\n                                    children: ((_row_original_vessel = row.original.vessel) === null || _row_original_vessel === void 0 ? void 0 : _row_original_vessel.title) || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\"\n        },\n        {\n            accessorKey: \"vessel.title\",\n            header: \"Vessel\",\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                var _row_original_vessel;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: ((_row_original_vessel = row.original.vessel) === null || _row_original_vessel === void 0 ? void 0 : _row_original_vessel.title) || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"center\"\n        },\n        {\n            accessorKey: \"created\",\n            header: \"Date\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: formatDate(row.original.created)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"center\"\n        },\n        {\n            accessorKey: \"member\",\n            header: \"Member\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: getMemberName(row.original)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"right\"\n        }\n    ]);\n    // Function to render expanded content\n    const renderExpandedContent = (rowData)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                rowData.type === \"TowingChecklist\" && rowData.towingChecklistID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_forms_risk_analysis__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    selectedEvent: false,\n                    onSidebarClose: false,\n                    logBookConfig: false,\n                    currentTrip: false,\n                    crewMembers: false,\n                    towingChecklistID: rowData.towingChecklistID,\n                    setTowingChecklistID: ()=>{},\n                    setAllChecked: setAllChecked,\n                    noSheet: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 25\n                }, this),\n                rowData.type === \"BarCrossingChecklist\" && rowData.barCrossingChecklistID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_forms_bar_crossing_risk_analysis__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    selectedEvent: false,\n                    onSidebarClose: false,\n                    logBookConfig: false,\n                    currentTrip: false,\n                    crewMembers: false,\n                    barCrossingChecklistID: rowData.barCrossingChecklistID,\n                    setBarCrossingChecklistID: ()=>{},\n                    setAllChecked: setAllChecked,\n                    noSheet: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n            lineNumber: 162,\n            columnNumber: 13\n        }, this);\n    };\n    // Function to determine if a row can be expanded\n    const canExpand = (rowData)=>{\n        return rowData.type === \"TowingChecklist\" && !!rowData.towingChecklistID || rowData.type === \"BarCrossingChecklist\" && !!rowData.barCrossingChecklistID;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        ne: \"RiskFactor\"\n                    }\n                }\n            }\n        });\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ListHeader, {\n                title: \"Risk Evaluations\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                lineNumber: 229,\n                columnNumber: 13\n            }, this),\n            riskFactors && riskFactors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__.CollapsibleDataTable, {\n                columns: columns,\n                data: riskFactors,\n                showToolbar: false,\n                collapsible: true,\n                renderExpandedContent: renderExpandedContent,\n                canExpand: canExpand,\n                pageSize: 20,\n                showPageSizeSelector: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                lineNumber: 232,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskEvaluations, \"mUX5wKsDG21uZJMo3HDup4aTqns=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery\n    ];\n});\n_c = RiskEvaluations;\nvar _c;\n$RefreshReg$(_c, \"RiskEvaluations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/risk-evaluations/risk-evaluations.tsx\n"));

/***/ })

});