"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/app/ui/risk-evaluations/risk-evaluations.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/risk-evaluations/risk-evaluations.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskEvaluations; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _logbook_forms_risk_analysis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/forms/risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _logbook_forms_bar_crossing_risk_analysis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../logbook/forms/bar-crossing-risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx\");\n/* harmony import */ var _components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/collapsible-data-table */ \"(app-pages-browser)/./src/components/collapsible-data-table.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Helper function to get the display name for risk evaluation type\nconst getRiskEvaluationType = (checklist)=>{\n    const typeMap = {\n        TowingChecklist: \"Towing Checklist\",\n        DangerousGoods: \"Dangerous Goods\",\n        BarCrossingChecklist: \"Bar Crossing Checklist\"\n    };\n    let typeName = typeMap[checklist.type] || checklist.type;\n    // Add ID if available\n    if (checklist.type === \"TowingChecklist\" && checklist.towingChecklistID && checklist.towingChecklistID > 0) {\n        typeName += \": #\".concat(checklist.towingChecklistID);\n    } else if (checklist.type === \"DangerousGoods\" && checklist.dangerousGoodsID && checklist.dangerousGoodsID > 0) {\n        typeName += \": #\".concat(checklist.dangerousGoodsID);\n    } else if (checklist.type === \"BarCrossingChecklist\" && checklist.barCrossingChecklistID && checklist.barCrossingChecklistID > 0) {\n        typeName += \": #\".concat(checklist.barCrossingChecklistID);\n    }\n    return typeName;\n};\n// Helper function to get member name\nconst getMemberName = (checklist)=>{\n    let member = null;\n    if (checklist.type === \"TowingChecklist\") {\n        var _checklist_towingChecklist;\n        member = (_checklist_towingChecklist = checklist.towingChecklist) === null || _checklist_towingChecklist === void 0 ? void 0 : _checklist_towingChecklist.member;\n    } else if (checklist.type === \"DangerousGoods\") {\n        var _checklist_dangerousGoods;\n        member = (_checklist_dangerousGoods = checklist.dangerousGoods) === null || _checklist_dangerousGoods === void 0 ? void 0 : _checklist_dangerousGoods.member;\n    } else if (checklist.type === \"BarCrossingChecklist\") {\n        var _checklist_barCrossingChecklist;\n        member = (_checklist_barCrossingChecklist = checklist.barCrossingChecklist) === null || _checklist_barCrossingChecklist === void 0 ? void 0 : _checklist_barCrossingChecklist.member;\n    }\n    if (!member) return \"\";\n    const firstName = member.firstName || \"\";\n    const surname = member.surname || \"\";\n    return \"\".concat(firstName, \" \").concat(surname).trim();\n};\n// Helper function to format date\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(new Date(dateString), \"dd/MM/yyyy\");\n    } catch (e) {\n        return dateString;\n    }\n};\nfunction RiskEvaluations() {\n    _s();\n    const [riskFactors, setRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Define columns for the collapsible table\n    const columns = (0,_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__.createColumns)([\n        {\n            accessorKey: \"type\",\n            header: \"Risk Evaluations\",\n            cell: (param)=>{\n                let { row } = param;\n                var _row_original_vessel;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full space-y-2 landscape:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"small:text-nowrap\",\n                                    children: getRiskEvaluationType(row.original)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, this),\n                                getMemberName(row.original) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"tablet-md:hidden text-card-foreground/80\",\n                                    children: [\n                                        \"Memeber: \",\n                                        getMemberName(row.original)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm landscape:hidden flex justify-between gap-2.5 text-card-foreground/80\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" text-nowrap\",\n                                    children: [\n                                        \"Date:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatDate(row.original.created)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" tablet-md:hidden\",\n                                    children: ((_row_original_vessel = row.original.vessel) === null || _row_original_vessel === void 0 ? void 0 : _row_original_vessel.title) || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\"\n        },\n        {\n            accessorKey: \"vessel.title\",\n            header: \"Vessel\",\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                var _row_original_vessel;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: ((_row_original_vessel = row.original.vessel) === null || _row_original_vessel === void 0 ? void 0 : _row_original_vessel.title) || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"center\"\n        },\n        {\n            accessorKey: \"created\",\n            header: \"Date\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: formatDate(row.original.created)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"center\"\n        },\n        {\n            accessorKey: \"member\",\n            header: \"Member\",\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: getMemberName(row.original)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"right\"\n        }\n    ]);\n    // Function to render expanded content\n    const renderExpandedContent = (rowData)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                rowData.type === \"TowingChecklist\" && rowData.towingChecklistID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_forms_risk_analysis__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    selectedEvent: false,\n                    onSidebarClose: false,\n                    logBookConfig: false,\n                    currentTrip: false,\n                    crewMembers: false,\n                    towingChecklistID: rowData.towingChecklistID,\n                    setTowingChecklistID: ()=>{},\n                    setAllChecked: setAllChecked,\n                    noSheet: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 25\n                }, this),\n                rowData.type === \"BarCrossingChecklist\" && rowData.barCrossingChecklistID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_forms_bar_crossing_risk_analysis__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    selectedEvent: false,\n                    onSidebarClose: false,\n                    logBookConfig: false,\n                    currentTrip: false,\n                    crewMembers: false,\n                    barCrossingChecklistID: rowData.barCrossingChecklistID,\n                    setBarCrossingChecklistID: ()=>{},\n                    setAllChecked: setAllChecked,\n                    noSheet: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n            lineNumber: 170,\n            columnNumber: 13\n        }, this);\n    };\n    // Function to determine if a row can be expanded\n    const canExpand = (rowData)=>{\n        return rowData.type === \"TowingChecklist\" && !!rowData.towingChecklistID || rowData.type === \"BarCrossingChecklist\" && !!rowData.barCrossingChecklistID;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        ne: \"RiskFactor\"\n                    }\n                }\n            }\n        });\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ListHeader, {\n                title: \"Risk Evaluations\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                lineNumber: 237,\n                columnNumber: 13\n            }, this),\n            riskFactors && riskFactors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__.CollapsibleDataTable, {\n                columns: columns,\n                data: riskFactors,\n                showToolbar: false,\n                collapsible: true,\n                renderExpandedContent: renderExpandedContent,\n                canExpand: canExpand,\n                pageSize: 20,\n                showPageSizeSelector: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                lineNumber: 240,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskEvaluations, \"mUX5wKsDG21uZJMo3HDup4aTqns=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery\n    ];\n});\n_c = RiskEvaluations;\nvar _c;\n$RefreshReg$(_c, \"RiskEvaluations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/risk-evaluations/risk-evaluations.tsx\n"));

/***/ })

});