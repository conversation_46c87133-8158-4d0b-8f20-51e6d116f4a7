"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/app/ui/risk-evaluations/risk-evaluations.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/risk-evaluations/risk-evaluations.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskEvaluations; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _logbook_forms_risk_analysis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/forms/risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _logbook_forms_bar_crossing_risk_analysis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../logbook/forms/bar-crossing-risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx\");\n/* harmony import */ var _components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/collapsible-data-table */ \"(app-pages-browser)/./src/components/collapsible-data-table.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Helper function to get the display name for risk evaluation type\nconst getRiskEvaluationType = (checklist)=>{\n    const typeMap = {\n        TowingChecklist: \"Towing Checklist\",\n        DangerousGoods: \"Dangerous Goods\",\n        BarCrossingChecklist: \"Bar Crossing Checklist\"\n    };\n    let typeName = typeMap[checklist.type] || checklist.type;\n    // Add ID if available\n    if (checklist.type === \"TowingChecklist\" && checklist.towingChecklistID && checklist.towingChecklistID > 0) {\n        typeName += \": #\".concat(checklist.towingChecklistID);\n    } else if (checklist.type === \"DangerousGoods\" && checklist.dangerousGoodsID && checklist.dangerousGoodsID > 0) {\n        typeName += \": #\".concat(checklist.dangerousGoodsID);\n    } else if (checklist.type === \"BarCrossingChecklist\" && checklist.barCrossingChecklistID && checklist.barCrossingChecklistID > 0) {\n        typeName += \": #\".concat(checklist.barCrossingChecklistID);\n    }\n    return typeName;\n};\n// Helper function to get member name\nconst getMemberName = (checklist)=>{\n    let member = null;\n    if (checklist.type === \"TowingChecklist\") {\n        var _checklist_towingChecklist;\n        member = (_checklist_towingChecklist = checklist.towingChecklist) === null || _checklist_towingChecklist === void 0 ? void 0 : _checklist_towingChecklist.member;\n    } else if (checklist.type === \"DangerousGoods\") {\n        var _checklist_dangerousGoods;\n        member = (_checklist_dangerousGoods = checklist.dangerousGoods) === null || _checklist_dangerousGoods === void 0 ? void 0 : _checklist_dangerousGoods.member;\n    } else if (checklist.type === \"BarCrossingChecklist\") {\n        var _checklist_barCrossingChecklist;\n        member = (_checklist_barCrossingChecklist = checklist.barCrossingChecklist) === null || _checklist_barCrossingChecklist === void 0 ? void 0 : _checklist_barCrossingChecklist.member;\n    }\n    if (!member) return \"\";\n    const firstName = member.firstName || \"\";\n    const surname = member.surname || \"\";\n    return \"\".concat(firstName, \" \").concat(surname).trim();\n};\n// Helper function to format date\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(new Date(dateString), \"dd/MM/yyyy\");\n    } catch (e) {\n        return dateString;\n    }\n};\nfunction RiskEvaluations() {\n    _s();\n    const [riskFactors, setRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Define columns for the collapsible table\n    const columns = (0,_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__.createColumns)([\n        {\n            accessorKey: \"type\",\n            header: \"Risk Evaluations\",\n            cell: (param)=>{\n                let { row } = param;\n                var _row_original_vessel;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"small:text-nowrap\",\n                                    children: getRiskEvaluationType(row.original)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, this),\n                                getMemberName(row.original) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm tablet-md:hidden text-card-foreground/80\",\n                                    children: [\n                                        \"Memeber: \",\n                                        getMemberName(row.original)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm landscape:hidden flex justify-between text-card-foreground/80\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        \"Date:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatDate(row.original.created)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" tablet-md:hidden\",\n                                    children: ((_row_original_vessel = row.original.vessel) === null || _row_original_vessel === void 0 ? void 0 : _row_original_vessel.title) || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\"\n        },\n        {\n            accessorKey: \"vessel.title\",\n            header: \"Vessel\",\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                var _row_original_vessel;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: ((_row_original_vessel = row.original.vessel) === null || _row_original_vessel === void 0 ? void 0 : _row_original_vessel.title) || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"center\"\n        },\n        {\n            accessorKey: \"created\",\n            header: \"Date\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: formatDate(row.original.created)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"center\"\n        },\n        {\n            accessorKey: \"member\",\n            header: \"Member\",\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: getMemberName(row.original)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 32\n                }, this);\n            },\n            cellAlignment: \"right\"\n        }\n    ]);\n    // Function to render expanded content\n    const renderExpandedContent = (rowData)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                rowData.type === \"TowingChecklist\" && rowData.towingChecklistID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_forms_risk_analysis__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    selectedEvent: false,\n                    onSidebarClose: false,\n                    logBookConfig: false,\n                    currentTrip: false,\n                    crewMembers: false,\n                    towingChecklistID: rowData.towingChecklistID,\n                    setTowingChecklistID: ()=>{},\n                    setAllChecked: setAllChecked,\n                    noSheet: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 25\n                }, this),\n                rowData.type === \"BarCrossingChecklist\" && rowData.barCrossingChecklistID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_forms_bar_crossing_risk_analysis__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    selectedEvent: false,\n                    onSidebarClose: false,\n                    logBookConfig: false,\n                    currentTrip: false,\n                    crewMembers: false,\n                    barCrossingChecklistID: rowData.barCrossingChecklistID,\n                    setBarCrossingChecklistID: ()=>{},\n                    setAllChecked: setAllChecked,\n                    noSheet: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n            lineNumber: 170,\n            columnNumber: 13\n        }, this);\n    };\n    // Function to determine if a row can be expanded\n    const canExpand = (rowData)=>{\n        return rowData.type === \"TowingChecklist\" && !!rowData.towingChecklistID || rowData.type === \"BarCrossingChecklist\" && !!rowData.barCrossingChecklistID;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        ne: \"RiskFactor\"\n                    }\n                }\n            }\n        });\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ListHeader, {\n                title: \"Risk Evaluations\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                lineNumber: 237,\n                columnNumber: 13\n            }, this),\n            riskFactors && riskFactors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_5__.CollapsibleDataTable, {\n                columns: columns,\n                data: riskFactors,\n                showToolbar: false,\n                collapsible: true,\n                renderExpandedContent: renderExpandedContent,\n                canExpand: canExpand,\n                pageSize: 20,\n                showPageSizeSelector: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-evaluations.tsx\",\n                lineNumber: 240,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskEvaluations, \"mUX5wKsDG21uZJMo3HDup4aTqns=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery\n    ];\n});\n_c = RiskEvaluations;\nvar _c;\n$RefreshReg$(_c, \"RiskEvaluations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/risk-evaluations/risk-evaluations.tsx\n"));

/***/ })

});